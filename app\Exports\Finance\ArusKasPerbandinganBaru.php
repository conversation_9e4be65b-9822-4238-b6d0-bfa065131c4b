<?php

namespace App\Exports\Finance;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;

class ArusKasPerbandinganBaru implements FromArray, WithHeadings, WithStyles, WithColumnWidths
{
    protected $data;

    public function __construct($data) {
        $this->data = $data;
    }

    public function array(): array
    {
        $rows = [];

        $rows[] = [env('PROJECT'), '', ''];
        $rows[] = ['ARUS KAS PERBANDINGAN BARU', '', ''];
        $rows[] = [
            'Cabang: ' . ($this->data['company'] ?? 'Semua Cabang'),
            '',
            ''
        ];
        $rows[] = [
            'Periode 1: ' . ($this->data['start1'] ?? '') . ' s/d ' . ($this->data['end1'] ?? ''),
            'Periode 2: ' . ($this->data['start2'] ?? '') . ' s/d ' . ($this->data['end2'] ?? ''),
            ''
        ];
        $rows[] = ['', '', ''];
        $rows[] = ['Akun & Kategori', 'Periode 1', 'Periode 2'];

        foreach ($this->data['data'] as $item) {
            $amount1 = '';
            $amount2 = '';
            if (!$item->is_header) {
                $amount1 = number_format($item->amount1, 0, ',', '.');
                $amount2 = number_format($item->amount2, 0, ',', '.');
            }
            $rows[] = [$item->name, $amount1, $amount2];
        }

        return $rows;
    }

    public function headings(): array
    {
        return [];
    }

    public function styles(Worksheet $sheet)
    {
        $lastRow = count($this->array());
        
        return [
            // Header styles
            1 => [
                'font' => ['bold' => true, 'size' => 14],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER]
            ],
            2 => [
                'font' => ['bold' => true, 'size' => 12],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER]
            ],
            3 => [
                'font' => ['bold' => true],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_LEFT]
            ],
            4 => [
                'font' => ['bold' => true],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_LEFT]
            ],
            6 => [
                'font' => ['bold' => true],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['argb' => 'FFE0E0E0']
                ]
            ],
            // Data rows
            "7:$lastRow" => [
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_LEFT]
            ]
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 50,
            'B' => 20,
            'C' => 20,
        ];
    }
}
